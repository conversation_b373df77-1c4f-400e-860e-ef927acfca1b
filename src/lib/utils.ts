export const chunk = <T>(arr: T[], size: number): T[][] => {
  const result: T[][] = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
};


export const batchStripeOperation = async <T>(
    operation: (items: T[]) => Promise<void>,
    items: T[],
    batchSize: number
  ) => {
    const chunks = chunk(items, batchSize);
    for (const chunk of chunks) {
      await operation(chunk);
    }
  };
  
  