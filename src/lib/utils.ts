export const chunk = <T>(arr: T[], size: number): T[][] => {
  const result: T[][] = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
};

/**
 * Generic pagination utility for APIs that follow cursor-based pagination pattern
 * @param fetchPage - Function that fetches a page of data given a cursor
 * @param processItem - Function to process each item in the page
 * @param options - Configuration options
 */
export const paginateAndProcess = async <T extends { id: string }>(
  fetchPage: (cursor?: string) => Promise<{ data: T[]; has_more: boolean }>,
  processItem: (item: T) => Promise<void>,
  options: {
    limit?: number;
    batchSize?: number;
  } = {}
): Promise<void> => {
  const { limit = 100, batchSize = 100 } = options;

  let hasMore = true;
  let cursor: string | null = null;

  while (hasMore) {
    const page = await fetchPage(cursor || undefined);

    // Process items in batches if batchSize is smaller than page size
    const items = page.data;
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      await Promise.all(batch.map(processItem));
    }

    // Update cursor and hasMore for next iteration
    cursor = items.at(-1)?.id || null;
    hasMore = page.has_more;
  }
};

/**
 * Generic pagination utility that collects all items across pages
 * @param fetchPage - Function that fetches a page of data given a cursor
 * @param options - Configuration options
 */
export const paginateAndCollect = async <T extends { id: string }>(
  fetchPage: (cursor?: string) => Promise<{ data: T[]; has_more: boolean }>,
  options: {
    limit?: number;
    maxItems?: number;
  } = {}
): Promise<T[]> => {
  const { limit = 100, maxItems } = options;

  const allItems: T[] = [];
  let hasMore = true;
  let cursor: string | null = null;

  while (hasMore && (!maxItems || allItems.length < maxItems)) {
    const page = await fetchPage(cursor || undefined);

    const itemsToAdd = maxItems
      ? page.data.slice(0, maxItems - allItems.length)
      : page.data;

    allItems.push(...itemsToAdd);

    // Update cursor and hasMore for next iteration
    cursor = page.data.at(-1)?.id || null;
    hasMore = page.has_more && (!maxItems || allItems.length < maxItems);
  }

  return allItems;
};
